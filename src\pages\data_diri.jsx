import { useEffect, useState } from "react";
import { useAuth } from "../hooks/useAuth.js";
import { apiService, formatBookingData } from "../services/api.js";

export default function DataDiri({ isOpen, onSubmit, onClose }) {
  const { isAuthenticated, user, token } = useAuth();
  const [personalData, setPersonalData] = useState({
    name: "",
    phone: "",
    activityDate: "",
    hotel: "",
    needTransport: null, // true/false
    transportType: "",
    driverPhone: "",
  });

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [showDebug, setShowDebug] = useState(false);

  const transportOptions = [
    { value: "Medium Car", label: "Medium Car", price: "Rp 350.000" },
    { value: "HI ACE", label: "HI ACE", price: "Rp 950.000" },
    {
      value: "Full Day Medium Car",
      label: "Full Day Medium Car",
      price: "Rp 550.000",
    },
    {
      value: "Full Day HI ACE",
      label: "Full Day HI ACE",
      price: "Rp 1.550.000",
    },
  ];

  // Handler untuk personal data modal
  const handlePersonalDataChange = (e) => {
    const { name, value, type } = e.target;
    setPersonalData({
      ...personalData,
      [name]: type === "radio" ? value === "true" : value,
    });
  };

  const handleTransportChange = (value) => {
    setPersonalData({
      ...personalData,
      needTransport: value,
      transportType: value ? personalData.transportType : "",
      driverPhone: value ? "" : personalData.driverPhone,
    });
  };

  const handleTransportTypeSelect = (option) => {
    setPersonalData({
      ...personalData,
      transportType: option.value,
    });
    setIsDropdownOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isDropdownOpen && !event.target.closest(".transport-dropdown")) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDropdownOpen]);

  const validatePersonalData = () => {
    const {
      name,
      phone,
      activityDate,
      hotel,
      needTransport,
      transportType,
      driverPhone,
    } = personalData;

    if (!name || !phone || !activityDate || !hotel || needTransport === null) {
      return false;
    }

    if (needTransport && !transportType) {
      return false;
    }

    if (!needTransport && !driverPhone) {
      return false;
    }

    return true;
  };

  // Debug functions
  const testServerConnection = async () => {
    try {
      console.log("Testing server connection...");
      const response = await fetch("/booking", {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });
      console.log(
        "Server connection test:",
        response.status,
        response.statusText
      );
    } catch (error) {
      console.error("Server connection failed:", error);
    }
  };

  const testBookingAPI = async () => {
    try {
      console.log("Testing booking API...");
      const testData = {
        name: "Test User",
        phone: "081234567890",
        date: new Date().toISOString().split("T")[0],
        guests: 1,
        mainActivity: "Cycling",
        total: 100000,
      };

      const response = await fetch("/booking", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(testData),
      });

      console.log("Booking API test:", response.status, response.statusText);
      const data = await response.text();
      console.log("Response:", data);
    } catch (error) {
      console.error("Booking API test failed:", error);
    }
  };

  const testBookingWithAuth = async () => {
    try {
      console.log("Testing booking API with auth...");
      const testData = {
        name: "Test User",
        phone: "081234567890",
        date: new Date().toISOString().split("T")[0],
        guests: 1,
        mainActivity: "Cycling",
        total: 100000,
      };

      const headers = {
        "Content-Type": "application/json",
      };

      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }

      const response = await fetch("/booking", {
        method: "POST",
        headers: headers,
        body: JSON.stringify(testData),
      });

      console.log(
        "Booking API with auth test:",
        response.status,
        response.statusText
      );
      const data = await response.text();
      console.log("Response:", data);
    } catch (error) {
      console.error("Booking API with auth test failed:", error);
    }
  };

  const handleTestAPI = async () => {
    console.log("=== TESTING API ENDPOINTS ===");
    await testServerConnection();
    await testBookingAPI();
    await testBookingWithAuth();
  };

  const handlePersonalDataSubmit = async () => {
    // Clear previous errors
    setError("");

    console.log("=== MEMULAI PROSES BOOKING ===");
    console.log("Status autentikasi:", isAuthenticated);
    console.log("Data user:", user);
    console.log("Token:", token);
    console.log("Data personal:", personalData);

    // Check authentication
    if (!isAuthenticated) {
      setError("Anda harus login terlebih dahulu untuk melakukan booking");
      return;
    }

    // Validasi data dengan logging detail
    const {
      name,
      phone,
      activityDate,
      hotel,
      needTransport,
      transportType,
      driverPhone,
    } = personalData;

    console.log("=== VALIDASI DATA ===");
    console.log("Name:", name);
    console.log("Phone:", phone);
    console.log("Activity Date:", activityDate);
    console.log("Hotel:", hotel);
    console.log("Need Transport:", needTransport);
    console.log("Transport Type:", transportType);
    console.log("Driver Phone:", driverPhone);

    const isValid = validatePersonalData();
    console.log("Hasil validasi:", isValid);

    if (!isValid) {
      let errorMsg = "Mohon lengkapi data berikut: ";
      const missingFields = [];

      if (!name) missingFields.push("Nama Lengkap");
      if (!phone) missingFields.push("Nomor Telepon");
      if (!activityDate) missingFields.push("Tanggal Kegiatan");
      if (!hotel) missingFields.push("Hotel");
      if (needTransport === null) missingFields.push("Pilihan Transport");
      if (needTransport && !transportType)
        missingFields.push("Jenis Transport");
      if (!needTransport && !driverPhone)
        missingFields.push("Nomor Telepon Driver");

      errorMsg += missingFields.join(", ");
      setError(errorMsg);
      return;
    }

    setIsLoading(true);

    try {
      // Format data for booking API
      const bookingData = formatBookingData(personalData);
      console.log("=== DATA BOOKING YANG AKAN DIKIRIM ===");
      console.log("Booking Data:", JSON.stringify(bookingData, null, 2));

      // Call booking API
      const response = await apiService.createBooking(bookingData);
      console.log("=== RESPONS DARI SERVER ===");
      console.log("Response:", response);

      // Show success message
      alert("Booking berhasil dibuat!");

      // Call parent onSubmit with the response data
      onSubmit({
        personalData,
        bookingResponse: response,
      });

      // Close modal
      onClose();
    } catch (error) {
      console.error("=== ERROR SAAT BOOKING ===");
      console.error("Error object:", error);
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);

      setError(
        error.message ||
          "Terjadi kesalahan saat membuat booking. Silakan coba lagi."
      );
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green p-6">
          <h2 className="text-2xl font-bold text-white text-center">
            Data Diri Peserta
          </h2>
          <p className="text-ubud-cream text-center mt-2">
            Mohon lengkapi data diri sebelum melanjutkan booking
          </p>
        </div>

        <div className="p-6 space-y-4">
          {/* Error Message */}
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {/* Authentication Warning */}
          {!isAuthenticated && (
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg">
              Anda harus login terlebih dahulu untuk melakukan booking
            </div>
          )}

          {/* Nama */}
          <div>
            <label
              htmlFor="name"
              className="block text-ubud-dark-green font-semibold mb-2"
            >
              Nama Lengkap *
            </label>
            <input
              id="name"
              type="text"
              name="name"
              value={personalData.name}
              onChange={handlePersonalDataChange}
              placeholder="Masukkan nama lengkap"
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
            />
          </div>

          {/* Nomor Telepon */}
          <div>
            <label
              htmlFor="phone"
              className="block text-ubud-dark-green font-semibold mb-2"
            >
              Nomor Telepon *
            </label>
            <input
              id="phone"
              type="tel"
              name="phone"
              value={personalData.phone}
              onChange={handlePersonalDataChange}
              placeholder="Masukkan nomor telepon"
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
            />
          </div>

          {/* Tanggal Kegiatan */}
          <div>
            <label
              htmlFor="activityDate"
              className="block text-ubud-dark-green font-semibold mb-2"
            >
              Tanggal Kegiatan *
            </label>
            <input
              id="activityDate"
              type="date"
              name="activityDate"
              value={personalData.activityDate}
              onChange={handlePersonalDataChange}
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
            />
          </div>

          {/* Hotel */}
          <div>
            <label
              htmlFor="hotel"
              className="block text-ubud-dark-green font-semibold mb-2"
            >
              Hotel *
            </label>
            <input
              id="hotel"
              type="text"
              name="hotel"
              value={personalData.hotel}
              onChange={handlePersonalDataChange}
              placeholder="Masukkan nama hotel"
              className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
            />
          </div>

          {/* Transport */}
          <div>
            <label className="block text-ubud-dark-green font-semibold mb-2">
              Apakah memerlukan transport? *
            </label>
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => handleTransportChange(true)}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  personalData.needTransport === true
                    ? "bg-ubud-dark-green text-white"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                Ya
              </button>
              <button
                type="button"
                onClick={() => handleTransportChange(false)}
                className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                  personalData.needTransport === false
                    ? "bg-ubud-dark-green text-white"
                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                }`}
              >
                Tidak
              </button>
            </div>
          </div>

          {/* Transport Type - hanya muncul jika memerlukan transport */}
          {personalData.needTransport === true && (
            <div>
              <label className="block text-ubud-dark-green font-semibold mb-2">
                Jenis Transport *
              </label>
              <div className="relative transport-dropdown">
                <button
                  type="button"
                  onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors text-left bg-white flex justify-between items-center"
                >
                  <span>
                    {personalData.transportType || "Pilih jenis transport"}
                  </span>
                  <svg
                    className={`w-5 h-5 transition-transform ${
                      isDropdownOpen ? "rotate-180" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>

                {isDropdownOpen && (
                  <div className="absolute z-10 w-full mt-1 bg-white border-2 border-gray-200 rounded-lg shadow-lg">
                    {transportOptions.map((option) => (
                      <button
                        key={option.value}
                        type="button"
                        onClick={() => handleTransportTypeSelect(option)}
                        className="w-full px-4 py-3 text-left hover:bg-gray-50 flex justify-between items-center border-b border-gray-100 last:border-b-0"
                      >
                        <span>{option.label}</span>
                        <span className="text-ubud-dark-green font-semibold">
                          {option.price}
                        </span>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Driver Phone - hanya muncul jika tidak memerlukan transport */}
          {personalData.needTransport === false && (
            <div>
              <label
                htmlFor="driverPhone"
                className="block text-ubud-dark-green font-semibold mb-2"
              >
                Nomor Telepon Driver *
              </label>
              <input
                id="driverPhone"
                type="tel"
                name="driverPhone"
                value={personalData.driverPhone}
                onChange={handlePersonalDataChange}
                placeholder="Masukkan nomor telepon driver"
                className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
              />
            </div>
          )}

          {/* Debug Section - hanya tampil di development */}
          {process.env.NODE_ENV === "development" && (
            <div className="border-t pt-4 mt-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-semibold text-gray-600">
                  Debug Tools
                </h3>
                <button
                  type="button"
                  onClick={() => setShowDebug(!showDebug)}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  {showDebug ? "Hide" : "Show"} Debug
                </button>
              </div>

              {showDebug && (
                <div className="space-y-2">
                  <button
                    type="button"
                    onClick={handleTestAPI}
                    className="w-full px-4 py-2 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                  >
                    Test API Endpoints
                  </button>
                  <div className="text-xs text-gray-500">
                    <p>
                      Auth Status:{" "}
                      {isAuthenticated
                        ? "✅ Authenticated"
                        : "❌ Not Authenticated"}
                    </p>
                    <p>User: {user?.name || "No user data"}</p>
                    <p>Token: {token ? "✅ Present" : "❌ Missing"}</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Tombol Submit */}
          <div className="flex justify-end pt-4">
            <button
              type="button"
              onClick={handlePersonalDataSubmit}
              disabled={isLoading || !isAuthenticated}
              className={`px-8 py-3 rounded-lg font-medium transition-colors ${
                isLoading || !isAuthenticated
                  ? "bg-gray-400 text-gray-600 cursor-not-allowed"
                  : "bg-ubud-dark-green text-white hover:bg-ubud-light-green"
              }`}
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Memproses...</span>
                </div>
              ) : (
                "Next"
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
